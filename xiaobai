export FORCE_TORCHRUN=1
export NNODES=1
export NODE_RANK=0
export NPROC_PER_NODE=8
export MASTER_ADDR=localhost
export MASTER_PORT=29500

llamafactory-cli train \
    --stage sft \
    --do_train True \
    --model_name_or_path /root/xiaobai/data/XiaomiMiMo/MiMo-7B-RL \
    --preprocessing_num_workers 16 \
    --finetuning_type lora \
    --template mimo \
    --flash_attn auto \
    --dataset_dir /root/xiaobai/LLaMA-Factory/data \
    --dataset identity \
    --cutoff_len 2048 \
    --learning_rate 5e-05 \
    --num_train_epochs 1000.0 \
    --max_samples 100000 \
    --per_device_train_batch_size 1 \
    --gradient_accumulation_steps 1 \
    --lr_scheduler_type cosine \
    --max_grad_norm 1.0 \
    --logging_steps 5 \
    --save_steps 100 \
    --warmup_steps 0 \
    --packing False \
    --enable_thinking True \
    --report_to none \
    --output_dir saves/MiMo-7B-Instruct/lora/train_2025-06-23-08-40-59 \
    --bf16 True \
    --plot_loss True \
    --trust_remote_code True \
    --gradient_checkpointing False \
    --ddp_timeout 180000000