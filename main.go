package main

import (
	"fmt"
	"log"

	"gorm.io/gorm"
)

func main() {
	db := NewDB()

	// 使用事务处理
	err := db.Transaction(func(tx *gorm.DB) error {
		// 创建书架
		log.Println("开始创建书架...")
		shelf1 := []Shelf{
			{
				Type: "文学",
			},
			{
				Type: "科技",
			},
		}

		if err := tx.Create(&shelf1).Error; err != nil {
			log.Printf("创建书架失败: %v\n", err)
			// 返回错误会导致事务回滚
			return err
		}
		log.Println("书架创建成功!")

		// 创建书籍
		log.Println("开始创建书籍...")
		book1 := Book{
			Title:   "Go语言编程",
			Author:  "小白兔",
			ShelfID: 1,
		}

		if err := tx.Create(&book1).Error; err != nil {
			log.Printf("创建书籍失败: %v\n", err)
			// 返回错误会导致事务回滚
			return err
		}
		log.Println("书籍创建成功!")

		// 返回nil提交事务
		return nil
	})

	// 检查事务执行结果
	if err != nil {
		log.Printf("事务执行失败: %v\n", err)
	} else {
		log.Println("事务执行成功，所有操作已提交!")
	}

	// 显示数据库中的记录数量
	var shelfCount int64
	db.Model(&Shelf{}).Count(&shelfCount)

	var bookCount int64
	db.Model(&Book{}).Count(&bookCount)

	fmt.Printf("数据库中有 %d 个书架和 %d 本书\n", shelfCount, bookCount)
}
